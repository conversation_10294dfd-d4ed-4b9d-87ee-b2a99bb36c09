/**
 * Next.js Middleware
 * 
 * Handles authentication and route protection at the edge.
 * Runs before pages are rendered to check authentication status.
 */

import { NextResponse } from 'next/server';
import { verifyToken } from './utils/auth';

// Define protected routes that require authentication
const protectedRoutes = [
  '/dashboard',
  '/admin',
  '/api/admin',
  '/api/protected',
];

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/api/auth',
];

// Define API routes that should be excluded from middleware
const excludedApiRoutes = [
  '/api/auth/send-otp',
  '/api/auth/verify-otp',
  '/api/auth/logout',
  '/api/auth/me',
];

/**
 * Check if a path matches any of the given patterns
 */
function matchesPath(pathname, patterns) {
  return patterns.some(pattern => {
    if (pattern.endsWith('*')) {
      return pathname.startsWith(pattern.slice(0, -1));
    }
    return pathname === pattern || pathname.startsWith(pattern + '/');
  });
}

/**
 * Extract token from request
 */
function getTokenFromRequest(request) {
  // Try cookie first
  const tokenCookie = request.cookies.get('adimas_token');
  if (tokenCookie) {
    return tokenCookie.value;
  }

  // Try Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

export async function middleware(request) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and Next.js internals
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/api/_next/') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // Skip middleware for excluded API routes
  if (matchesPath(pathname, excludedApiRoutes)) {
    return NextResponse.next();
  }

  // Check if route requires authentication
  const isProtectedRoute = matchesPath(pathname, protectedRoutes);
  const isPublicRoute = matchesPath(pathname, publicRoutes);

  // Get token from request
  const token = getTokenFromRequest(request);

  // If it's a protected route, verify authentication
  if (isProtectedRoute) {
    if (!token) {
      // No token, redirect to login
      const loginUrl = new URL('/', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

    try {
      // Verify token
      const payload = await verifyToken(token);
      
      // Token is valid, add user info to headers for API routes
      if (pathname.startsWith('/api/')) {
        const requestHeaders = new Headers(request.headers);
        requestHeaders.set('x-user-id', payload.id);
        requestHeaders.set('x-user-email', payload.email);
        requestHeaders.set('x-user-roles', JSON.stringify(payload.roles));

        return NextResponse.next({
          request: {
            headers: requestHeaders,
          },
        });
      }

      // For page routes, continue normally
      return NextResponse.next();
    } catch (error) {
      // Token is invalid, redirect to login
      console.error('Token verification failed in middleware:', error.message);
      
      const loginUrl = new URL('/', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      loginUrl.searchParams.set('error', 'session_expired');
      
      // Clear invalid token
      const response = NextResponse.redirect(loginUrl);
      response.cookies.delete('adimas_token');
      response.cookies.delete('adimas_refresh');
      
      return response;
    }
  }

  // If user is authenticated and trying to access login page, redirect to dashboard
  if (pathname === '/' && token) {
    try {
      await verifyToken(token);
      // Token is valid, redirect to dashboard
      return NextResponse.redirect(new URL('/dashboard', request.url));
    } catch (error) {
      // Token is invalid, clear it and continue to login
      const response = NextResponse.next();
      response.cookies.delete('adimas_token');
      response.cookies.delete('adimas_refresh');
      return response;
    }
  }

  // For all other routes, continue normally
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
