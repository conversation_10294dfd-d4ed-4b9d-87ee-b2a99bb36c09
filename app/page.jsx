import { getCurrentUserServer } from '@/utils/server-auth';
import { validateAuthConfig } from '@/config/auth';
import LoginPageClient from '@/components/auth/LoginPageClient';
import DashboardLayout from '@/components/dashboard/DashboardLayout';

/**
 * Main Application Page (Server Component)
 *
 * Server-side authentication check for better performance.
 * Shows login form for unauthenticated users or dashboard for authenticated users.
 */
export default async function Home() {
  // Validate auth configuration on server start
  validateAuthConfig();

  // Check authentication on server-side
  const user = await getCurrentUserServer();

  // If user is authenticated, show dashboard
  if (user) {
    return <DashboardLayout user={user} />;
  }

  // If not authenticated, show login page
  return <LoginPageClient />;
}
