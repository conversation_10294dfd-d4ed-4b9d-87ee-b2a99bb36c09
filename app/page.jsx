"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import LoginCard from '@/components/auth/LoginCard';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { validateAuthConfig } from '@/config/auth';

/**
 * Main Application Page
 *
 * Shows login form for unauthenticated users or dashboard for authenticated users.
 * Handles authentication state management and routing.
 */
export default function Home() {
  const { user, loading, isAuthenticated, logout } = useAuth();
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted (prevents hydration issues)
  useEffect(() => {
    setMounted(true);
    // Validate auth configuration on app start
    validateAuthConfig();
  }, []);

  // Handle successful login
  const handleLoginSuccess = (userData) => {
    // The useAuth hook will automatically update the user state
    console.log('Login successful:', userData);
  };

  // Handle logout
  const handleLogout = async () => {
    await logout();
  };

  // Show loading state during initial auth check
  if (!mounted || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Show dashboard if user is authenticated
  if (isAuthenticated && user) {
    return (
      <DashboardLayout
        user={user}
        onLogout={handleLogout}
      />
    );
  }

  // Show login form if user is not authenticated
  return (
    <LoginCard onLoginSuccess={handleLoginSuccess} />
  );
}
