import { getCurrentUserServer } from '@/utils/server-auth';
import { cookies } from 'next/headers';

/**
 * Test Authentication Page
 * 
 * Debug page to test server-side authentication and cookie handling.
 */
export default async function TestAuthPage() {
  const user = await getCurrentUserServer();
  const cookieStore = await cookies();
  const allCookies = cookieStore.getAll();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">
          Authentication Debug Page
        </h1>

        {/* User Status */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            User Status
          </h2>
          <div className="space-y-2">
            <p className="text-sm">
              <span className="font-medium">Authenticated:</span>{' '}
              <span className={user ? 'text-green-600' : 'text-red-600'}>
                {user ? 'Yes' : 'No'}
              </span>
            </p>
            {user && (
              <>
                <p className="text-sm">
                  <span className="font-medium">Email:</span> {user.email}
                </p>
                <p className="text-sm">
                  <span className="font-medium">Name:</span> {user.name || 'N/A'}
                </p>
                <p className="text-sm">
                  <span className="font-medium">Roles:</span> {user.roles?.join(', ') || 'None'}
                </p>
                <p className="text-sm">
                  <span className="font-medium">User ID:</span> {user.id}
                </p>
              </>
            )}
          </div>
        </div>

        {/* Cookies */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Cookies ({allCookies.length})
          </h2>
          {allCookies.length > 0 ? (
            <div className="space-y-2">
              {allCookies.map((cookie, index) => (
                <div key={index} className="text-sm">
                  <span className="font-medium">{cookie.name}:</span>{' '}
                  <span className="font-mono text-xs bg-gray-100 dark:bg-gray-700 px-1 rounded">
                    {cookie.value.length > 50 ? `${cookie.value.substring(0, 50)}...` : cookie.value}
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-gray-600 dark:text-gray-400">No cookies found</p>
          )}
        </div>

        {/* Actions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Actions
          </h2>
          <div className="space-x-4">
            <a
              href="/"
              className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Go to Home
            </a>
            <a
              href="/dashboard"
              className="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Go to Dashboard
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
