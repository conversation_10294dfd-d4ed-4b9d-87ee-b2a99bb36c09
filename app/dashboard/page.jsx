import { redirect } from 'next/navigation';
import { getCurrentUserServer } from '@/utils/server-auth';
import DashboardLayout from '@/components/dashboard/DashboardLayout';

/**
 * Dashboard Page (Server Component)
 *
 * Protected dashboard page with server-side authentication check.
 * Redirects to login if user is not authenticated.
 */
export default async function DashboardPage() {
  // Check authentication on server-side
  const user = await getCurrentUserServer();

  // If not authenticated, redirect to login
  if (!user) {
    redirect('/');
  }

  // Show dashboard for authenticated users
  return <DashboardLayout user={user} />;
}
