"use client";

import { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { useRouter } from 'next/navigation';

/**
 * Dashboard Page
 * 
 * Protected dashboard page that shows the main admin interface.
 * Redirects to login if user is not authenticated.
 */
export default function DashboardPage() {
  const { user, loading, isAuthenticated, logout } = useAuth();
  const [mounted, setMounted] = useState(false);
  const router = useRouter();

  // Ensure component is mounted (prevents hydration issues)
  useEffect(() => {
    setMounted(true);
  }, []);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (mounted && !loading && !isAuthenticated) {
      router.push('/');
    }
  }, [mounted, loading, isAuthenticated, router]);

  // Handle logout
  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  // Show loading state during initial auth check
  if (!mounted || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show dashboard if user is authenticated
  if (isAuthenticated && user) {
    return (
      <DashboardLayout 
        user={user} 
        onLogout={handleLogout}
      />
    );
  }

  // Return null while redirecting
  return null;
}
