/**
 * Logout API Route
 * 
 * Handles user logout by clearing authentication cookies
 * and invalidating tokens.
 */

import { createLogoutResponse, createErrorResponse } from '@/utils/auth';
import { authConfig } from '@/config/auth';

export async function POST(request) {
  try {
    // In a real implementation, you might want to:
    // 1. Add token to blacklist
    // 2. Log the logout event
    // 3. Clear any user sessions from database

    return createLogoutResponse({
      success: true,
      message: authConfig.success.logoutSuccess
    });

  } catch (error) {
    console.error('Logout error:', error);
    return createErrorResponse(authConfig.errors.serverError, 500);
  }
}

// Handle unsupported methods
export async function GET() {
  return createErrorResponse('Method not allowed', 405);
}

export async function PUT() {
  return createErrorResponse('Method not allowed', 405);
}

export async function DELETE() {
  return createErrorResponse('Method not allowed', 405);
}
