/**
 * Verify OTP API Route
 * 
 * Handles OTP verification and JWT token generation.
 * Includes rate limiting and attempt tracking.
 */

import { NextResponse } from 'next/server';
import { validateEmail, validateOtp, generateToken, generateRefreshToken, createAuthResponse, createErrorResponse } from '@/utils/auth';
import { rateLimit, createRateLimitResponse } from '@/utils/rateLimit';
import { authConfig } from '@/config/auth';
import { cache } from '@/utils/redis';

export async function POST(request) {
  try {
    // Parse request body
    const body = await request.json();
    const { email, otp } = body;

    // Validate input
    if (!validateEmail(email)) {
      return createErrorResponse(authConfig.errors.invalidEmail, 400);
    }

    if (!validateOtp(otp)) {
      return createErrorResponse(authConfig.errors.invalidOtp, 400);
    }

    // Check rate limits for OTP verification
    const rateLimitResult = await rateLimit(request, 'otp');
    if (!rateLimitResult.allowed) {
      return createRateLimitResponse(rateLimitResult, authConfig.errors.rateLimited);
    }

    // Retrieve OTP data from cache
    const otpKey = `otp:${email}`;
    let otpData;
    
    try {
      otpData = await cache.get(otpKey);
    } catch (cacheError) {
      console.error('Failed to retrieve OTP from cache:', cacheError);
      // Fallback to hardcoded validation for prototype
      if (email === authConfig.temp.adminEmail && otp === authConfig.temp.adminOtp) {
        otpData = {
          email,
          otp: authConfig.temp.adminOtp,
          expiresAt: Date.now() + 60000, // 1 minute buffer
          attempts: 0,
          maxAttempts: 5
        };
      }
    }

    // Check if OTP exists
    if (!otpData) {
      return createErrorResponse('OTP not found or expired. Please request a new one.', 400);
    }

    // Check if OTP is expired
    if (Date.now() > otpData.expiresAt) {
      // Clean up expired OTP
      try {
        await cache.del(otpKey);
      } catch (error) {
        console.error('Failed to delete expired OTP:', error);
      }
      return createErrorResponse(authConfig.errors.expiredOtp, 400);
    }

    // Check attempt limits
    if (otpData.attempts >= otpData.maxAttempts) {
      // Clean up OTP after max attempts
      try {
        await cache.del(otpKey);
      } catch (error) {
        console.error('Failed to delete OTP after max attempts:', error);
      }
      return createErrorResponse('Too many failed attempts. Please request a new OTP.', 429);
    }

    // Verify OTP
    if (otp !== otpData.otp) {
      // Increment attempt counter
      otpData.attempts += 1;
      try {
        await cache.set(otpKey, otpData, Math.ceil((otpData.expiresAt - Date.now()) / 1000));
      } catch (error) {
        console.error('Failed to update OTP attempts:', error);
      }
      
      const remainingAttempts = otpData.maxAttempts - otpData.attempts;
      return createErrorResponse(
        `Invalid OTP. ${remainingAttempts} attempts remaining.`, 
        400
      );
    }

    // OTP is valid - clean up
    try {
      await cache.del(otpKey);
    } catch (error) {
      console.error('Failed to delete verified OTP:', error);
    }

    // Create user payload for JWT
    const userPayload = {
      id: 'temp-admin-id', // In real implementation, this would come from database
      email: email,
      roles: ['super_admin'], // Temporary role assignment
      name: 'Admin User',
      loginAt: Date.now()
    };

    // Generate tokens
    const token = await generateToken(userPayload);
    const refreshToken = await generateRefreshToken(userPayload);

    // Create response with secure cookies
    const responseData = {
      success: true,
      message: authConfig.success.loginSuccess,
      user: {
        id: userPayload.id,
        email: userPayload.email,
        name: userPayload.name,
        roles: userPayload.roles
      }
    };

    return createAuthResponse(responseData, token, refreshToken);

  } catch (error) {
    console.error('Verify OTP error:', error);
    return createErrorResponse(authConfig.errors.serverError, 500);
  }
}

// Handle unsupported methods
export async function GET() {
  return createErrorResponse('Method not allowed', 405);
}

export async function PUT() {
  return createErrorResponse('Method not allowed', 405);
}

export async function DELETE() {
  return createErrorResponse('Method not allowed', 405);
}
