/**
 * Send OTP API Route
 * 
 * Handles email submission and OTP generation for login.
 * Includes rate limiting and email validation.
 */

import { NextResponse } from 'next/server';
import { validateEmail, createErrorResponse } from '@/utils/auth';
import { emailRateLimit, createRateLimitResponse } from '@/utils/rateLimit';
import { authConfig } from '@/config/auth';
import { cache } from '@/utils/redis';

export async function POST(request) {
  try {
    // Parse request body
    const body = await request.json();
    const { email } = body;

    // Validate email format
    if (!validateEmail(email)) {
      return createErrorResponse(authConfig.errors.invalidEmail, 400);
    }

    // Check rate limits
    const rateLimitResult = await emailRateLimit(request, email);
    if (!rateLimitResult.allowed) {
      return createRateLimitResponse(
        rateLimitResult, 
        `${authConfig.errors.rateLimited} ${rateLimitResult.limitType ? `(${rateLimitResult.limitType} limit)` : ''}`
      );
    }

    // Check if email is authorized (temporary hardcoded check)
    if (email !== authConfig.temp.adminEmail) {
      return createErrorResponse('Email not authorized for access', 403);
    }

    // Generate OTP session
    const otpData = {
      email,
      otp: authConfig.temp.adminOtp,
      expiresAt: Date.now() + (authConfig.temp.otpExpiryMinutes * 60 * 1000),
      attempts: 0,
      maxAttempts: 5
    };

    // Store OTP in cache (Redis or memory)
    const otpKey = `otp:${email}`;
    try {
      await cache.set(otpKey, otpData, authConfig.temp.otpExpiryMinutes * 60);
    } catch (cacheError) {
      console.error('Failed to store OTP in cache:', cacheError);
      // Continue without caching for now
    }

    // In a real implementation, you would send email here
    console.log(`📧 OTP for ${email}: ${authConfig.temp.adminOtp} (expires in ${authConfig.temp.otpExpiryMinutes} minutes)`);

    // Return success response
    return NextResponse.json({
      success: true,
      message: authConfig.success.otpSent,
      expiresIn: authConfig.temp.otpExpiryMinutes * 60, // seconds
      // In development, include OTP for testing
      ...(process.env.NODE_ENV === 'development' && {
        debug: {
          otp: authConfig.temp.adminOtp,
          note: 'OTP included for development testing only'
        }
      })
    });

  } catch (error) {
    console.error('Send OTP error:', error);
    return createErrorResponse(authConfig.errors.serverError, 500);
  }
}

// Handle unsupported methods
export async function GET() {
  return createErrorResponse('Method not allowed', 405);
}

export async function PUT() {
  return createErrorResponse('Method not allowed', 405);
}

export async function DELETE() {
  return createErrorResponse('Method not allowed', 405);
}
