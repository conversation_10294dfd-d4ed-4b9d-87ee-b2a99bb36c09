/**
 * Current User API Route
 * 
 * Returns current user information from <PERSON>W<PERSON> token.
 * Used to check authentication status and get user data.
 */

import { NextResponse } from 'next/server';
import { getCurrentUser, createErrorResponse } from '@/utils/auth';
import { authConfig } from '@/config/auth';

export async function GET(request) {
  try {
    // Get current user from token
    const user = await getCurrentUser(request);

    if (!user) {
      return createErrorResponse(authConfig.errors.unauthorized, 401);
    }

    // Return user information (excluding sensitive data)
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        roles: user.roles,
        loginAt: user.loginAt
      }
    });

  } catch (error) {
    console.error('Get current user error:', error);
    return createErrorResponse(authConfig.errors.serverError, 500);
  }
}

// Handle unsupported methods
export async function POST() {
  return createErrorResponse('Method not allowed', 405);
}

export async function PUT() {
  return createErrorResponse('Method not allowed', 405);
}

export async function DELETE() {
  return createErrorResponse('Method not allowed', 405);
}
