/**
 * Authentication Configuration
 * 
 * Centralized configuration for JWT authentication, rate limiting,
 * and temporary credentials for the prototype phase.
 */

export const authConfig = {
  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'fallback-secret-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
    algorithm: 'HS256',
    issuer: 'adimas-admin',
    audience: 'adimas-admin-users'
  },

  // Rate Limiting Configuration
  rateLimit: {
    // General API rate limiting
    general: {
      maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 5,
      windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
      message: 'Too many requests, please try again later.',
      standardHeaders: true,
      legacyHeaders: false
    },
    
    // Email-specific rate limiting (more restrictive)
    email: {
      maxRequests: parseInt(process.env.RATE_LIMIT_EMAIL_MAX) || 3,
      windowMs: parseInt(process.env.RATE_LIMIT_EMAIL_WINDOW_MS) || 60 * 60 * 1000, // 1 hour
      message: 'Too many email requests, please try again in an hour.',
      standardHeaders: true,
      legacyHeaders: false
    },

    // OTP verification rate limiting
    otp: {
      maxRequests: 10,
      windowMs: 15 * 60 * 1000, // 15 minutes
      message: 'Too many OTP attempts, please request a new code.',
      standardHeaders: true,
      legacyHeaders: false
    }
  },

  // Temporary Credentials (Prototype Phase)
  temp: {
    adminEmail: process.env.TEMP_ADMIN_EMAIL || '<EMAIL>',
    adminOtp: process.env.TEMP_ADMIN_OTP || '123456',
    otpExpiryMinutes: 10 // OTP expires in 10 minutes
  },

  // Cookie Configuration
  cookies: {
    tokenName: 'adimas_token',
    refreshTokenName: 'adimas_refresh',
    options: {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
      path: '/'
    }
  },

  // Security Headers
  security: {
    headers: {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    }
  },

  // Session Configuration
  session: {
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    updateAge: 24 * 60 * 60 * 1000, // Update session every 24 hours
    rolling: true // Extend session on activity
  },

  // Validation Rules
  validation: {
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      maxLength: 255,
      message: 'Please enter a valid email address'
    },
    otp: {
      pattern: /^\d{6}$/,
      length: 6,
      message: 'OTP must be 6 digits'
    }
  },

  // Error Messages
  errors: {
    invalidCredentials: 'Invalid email or OTP',
    expiredOtp: 'OTP has expired, please request a new one',
    invalidToken: 'Invalid or expired token',
    unauthorized: 'Unauthorized access',
    rateLimited: 'Too many requests, please try again later',
    serverError: 'Internal server error, please try again',
    invalidEmail: 'Please enter a valid email address',
    invalidOtp: 'Please enter a valid 6-digit OTP'
  },

  // Success Messages
  success: {
    otpSent: 'OTP sent to your email address',
    loginSuccess: 'Login successful',
    logoutSuccess: 'Logged out successfully'
  }
};

// Validate required environment variables
export function validateAuthConfig() {
  const required = ['JWT_SECRET'];
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.warn(`⚠️  Missing environment variables: ${missing.join(', ')}`);
    console.warn('Using fallback values for development. Set these in production!');
  }

  // Validate JWT secret strength
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
    console.warn('⚠️  JWT_SECRET should be at least 32 characters long for security');
  }

  return true;
}

// Export default configuration
export default authConfig;
