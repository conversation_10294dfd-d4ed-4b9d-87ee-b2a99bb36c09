"use client";

import { useState } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/card';
import { But<PERSON> } from '@heroui/button';
import { Chip } from '@heroui/chip';
import { Divider } from '@heroui/divider';

/**
 * DashboardLayout Component
 * 
 * Main dashboard layout with navigation, user info, and content area.
 * Includes logout functionality and responsive design.
 */
export default function DashboardLayout({ user, onLogout }) {
  const [loading, setLoading] = useState(false);

  // Handle logout
  const handleLogout = async () => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        onLogout();
      } else {
        console.error('Logout failed');
        // Still call onLogout to clear client-side state
        onLogout();
      }
    } catch (error) {
      console.error('Logout error:', error);
      // Still call onLogout to clear client-side state
      onLogout();
    } finally {
      setLoading(false);
    }
  };

  // Get user role display
  const getUserRoleDisplay = () => {
    if (!user.roles || user.roles.length === 0) return 'User';
    
    const roleMap = {
      'super_admin': 'Super Admin',
      'admin': 'Administrator',
      'manager': 'Manager',
      'staff': 'Staff',
      'viewer': 'Viewer'
    };

    return user.roles.map(role => roleMap[role] || role).join(', ');
  };

  // Get role color
  const getRoleColor = () => {
    if (!user.roles || user.roles.length === 0) return 'default';
    
    const primaryRole = user.roles[0];
    const colorMap = {
      'super_admin': 'danger',
      'admin': 'primary',
      'manager': 'secondary',
      'staff': 'success',
      'viewer': 'warning'
    };

    return colorMap[primaryRole] || 'default';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Title */}
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                Adimas Admin Panel
              </h1>
            </div>

            {/* User Info and Actions */}
            <div className="flex items-center gap-4">
              <div className="hidden sm:flex flex-col items-end">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {user.name || user.email}
                </span>
                <Chip 
                  size="sm" 
                  color={getRoleColor()} 
                  variant="flat"
                  className="text-xs"
                >
                  {getUserRoleDisplay()}
                </Chip>
              </div>
              
              <Button
                color="danger"
                variant="light"
                size="sm"
                onClick={handleLogout}
                isLoading={loading}
              >
                {loading ? 'Logging out...' : 'Logout'}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Welcome back, {user.name || user.email.split('@')[0]}!
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your inventory, orders, and system settings from this dashboard.
          </p>
        </div>

        {/* Dashboard Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Quick Stats Cards */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Products
              </h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    0
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Total products
                  </p>
                </div>
                <div className="text-blue-500">
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 2L3 7v11a1 1 0 001 1h12a1 1 0 001-1V7l-7-5zM9 9a1 1 0 112 0v4a1 1 0 11-2 0V9z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Orders
              </h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    0
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Total orders
                  </p>
                </div>
                <div className="text-green-500">
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Low Stock
              </h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    0
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Items need restock
                  </p>
                </div>
                <div className="text-orange-500">
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Quick Actions
            </h3>
          </CardHeader>
          <Divider />
          <CardBody>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Button 
                color="primary" 
                variant="flat" 
                className="h-20 flex-col"
                isDisabled
              >
                <svg className="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Add Product
              </Button>
              
              <Button 
                color="success" 
                variant="flat" 
                className="h-20 flex-col"
                isDisabled
              >
                <svg className="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                </svg>
                View Orders
              </Button>
              
              <Button 
                color="warning" 
                variant="flat" 
                className="h-20 flex-col"
                isDisabled
              >
                <svg className="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z" />
                  <path fillRule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
                Manage Inventory
              </Button>
              
              <Button 
                color="secondary" 
                variant="flat" 
                className="h-20 flex-col"
                isDisabled
              >
                <svg className="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
                Settings
              </Button>
            </div>
            
            <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>🚀 Welcome to your admin dashboard!</strong> This is a prototype version. 
                The quick action buttons are disabled as the full features are still being developed.
              </p>
            </div>
          </CardBody>
        </Card>

        {/* User Session Info */}
        <Card className="mt-6">
          <CardHeader>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Session Information
            </h3>
          </CardHeader>
          <Divider />
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-400">Email:</span>
                <span className="ml-2 text-gray-900 dark:text-white">{user.email}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-400">Role:</span>
                <span className="ml-2">
                  <Chip size="sm" color={getRoleColor()} variant="flat">
                    {getUserRoleDisplay()}
                  </Chip>
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-400">User ID:</span>
                <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">{user.id}</span>
              </div>
              <div>
                <span className="font-medium text-gray-600 dark:text-gray-400">Login Time:</span>
                <span className="ml-2 text-gray-900 dark:text-white">
                  {user.loginAt ? new Date(user.loginAt).toLocaleString() : 'Unknown'}
                </span>
              </div>
            </div>
          </CardBody>
        </Card>
      </main>
    </div>
  );
}
