"use client";

import { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader } from '@heroui/card';
import { Input } from '@heroui/input';
import { Button } from '@heroui/button';
import { Divider } from '@heroui/divider';
import { Chip } from '@heroui/chip';

/**
 * LoginCard Component
 * 
 * Handles the two-step login process:
 * 1. Email submission
 * 2. OTP verification
 */
export default function LoginCard({ onLoginSuccess }) {
  const [step, setStep] = useState('email'); // 'email' or 'otp'
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [countdown, setCountdown] = useState(0);
  const [rateLimitInfo, setRateLimitInfo] = useState(null);

  // Countdown timer for OTP expiry and rate limiting
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Format countdown time
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle email submission
  const handleEmailSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(data.message);
        setStep('otp');
        setCountdown(data.expiresIn || 600); // 10 minutes default
        
        // Show debug info in development
        if (data.debug) {
          console.log('🔐 Development OTP:', data.debug.otp);
        }
      } else {
        setError(data.error || 'Failed to send OTP');
        
        // Handle rate limiting
        if (response.status === 429) {
          setRateLimitInfo({
            retryAfter: data.retryAfter || 60,
            message: data.error
          });
          setCountdown(data.retryAfter || 60);
        }
      }
    } catch (err) {
      console.error('Email submission error:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle OTP verification
  const handleOtpSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess(data.message);
        // Call parent callback with user data
        onLoginSuccess(data.user);
      } else {
        setError(data.error || 'Invalid OTP');
        
        // Handle rate limiting
        if (response.status === 429) {
          setRateLimitInfo({
            retryAfter: data.retryAfter || 60,
            message: data.error
          });
          setCountdown(data.retryAfter || 60);
        }
      }
    } catch (err) {
      console.error('OTP verification error:', err);
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Reset to email step
  const handleBackToEmail = () => {
    setStep('email');
    setOtp('');
    setError('');
    setSuccess('');
    setCountdown(0);
    setRateLimitInfo(null);
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <Card className="w-full max-w-md shadow-2xl">
        <CardHeader className="flex flex-col gap-3 pb-6">
          <div className="flex flex-col items-center text-center">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
              Adimas Admin
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {step === 'email' ? 'Enter your email to continue' : 'Enter the OTP sent to your email'}
            </p>
          </div>
        </CardHeader>

        <Divider />

        <CardBody className="gap-4 pt-6">
          {/* Error Message */}
          {error && (
            <Chip color="danger" variant="flat" className="w-full">
              {error}
            </Chip>
          )}

          {/* Success Message */}
          {success && (
            <Chip color="success" variant="flat" className="w-full">
              {success}
            </Chip>
          )}

          {/* Rate Limit Info */}
          {rateLimitInfo && countdown > 0 && (
            <Chip color="warning" variant="flat" className="w-full">
              Rate limited. Try again in {formatTime(countdown)}
            </Chip>
          )}

          {/* Email Step */}
          {step === 'email' && (
            <form onSubmit={handleEmailSubmit} className="flex flex-col gap-4">
              <Input
                type="email"
                label="Email Address"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                isRequired
                isDisabled={loading || (rateLimitInfo && countdown > 0)}
                variant="bordered"
                classNames={{
                  input: "text-sm",
                  label: "text-sm font-medium"
                }}
              />
              
              {/* Development hint */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-gray-500 bg-gray-50 dark:bg-gray-800 p-2 rounded">
                  💡 Development: Use <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded"><EMAIL></code>
                </div>
              )}

              <Button
                type="submit"
                color="primary"
                isLoading={loading}
                isDisabled={!email || (rateLimitInfo && countdown > 0)}
                className="w-full"
              >
                {loading ? 'Sending OTP...' : 'Send OTP'}
              </Button>
            </form>
          )}

          {/* OTP Step */}
          {step === 'otp' && (
            <form onSubmit={handleOtpSubmit} className="flex flex-col gap-4">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                OTP sent to: <strong>{email}</strong>
                {countdown > 0 && (
                  <div className="text-xs mt-1">
                    Expires in: {formatTime(countdown)}
                  </div>
                )}
              </div>

              <Input
                type="text"
                label="OTP Code"
                placeholder="Enter 6-digit OTP"
                value={otp}
                onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                isRequired
                isDisabled={loading || (rateLimitInfo && countdown > 0)}
                variant="bordered"
                maxLength={6}
                classNames={{
                  input: "text-center text-lg tracking-widest font-mono",
                  label: "text-sm font-medium"
                }}
              />

              {/* Development hint */}
              {process.env.NODE_ENV === 'development' && (
                <div className="text-xs text-gray-500 bg-gray-50 dark:bg-gray-800 p-2 rounded">
                  💡 Development OTP: <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">123456</code>
                </div>
              )}

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="bordered"
                  onClick={handleBackToEmail}
                  isDisabled={loading}
                  className="flex-1"
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  color="primary"
                  isLoading={loading}
                  isDisabled={otp.length !== 6 || (rateLimitInfo && countdown > 0)}
                  className="flex-1"
                >
                  {loading ? 'Verifying...' : 'Verify OTP'}
                </Button>
              </div>
            </form>
          )}
        </CardBody>
      </Card>
    </div>
  );
}
