"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import LoginCard from './LoginCard';

/**
 * Login Page Client Component
 * 
 * Handles client-side login logic and redirects after successful authentication.
 * This component is only rendered when the user is not authenticated (server-side check).
 */
export default function LoginPageClient() {
  const router = useRouter();
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  // Handle successful login
  const handleLoginSuccess = async (userData) => {
    console.log('Login successful:', userData);
    setIsLoggingIn(true);
    
    // Small delay to show success state, then refresh the page
    // The server-side check will now show the dashboard
    setTimeout(() => {
      router.refresh(); // This will trigger server-side re-render
    }, 500);
  };

  // Show success state while redirecting
  if (isLoggingIn) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Login successful! Redirecting...</p>
        </div>
      </div>
    );
  }

  return <LoginCard onLoginSuccess={handleLoginSuccess} />;
}
