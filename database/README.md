# Adimas Admin Panel Database Schema

This document describes the comprehensive PostgreSQL database schema for the Adimas admin panel stock management system.

## Overview

The database schema supports a full-featured admin panel with the following core functionalities:
- User management with role-based access control
- Product catalog and inventory management
- Order processing and tracking
- Shipping and logistics
- Translation management
- Comprehensive audit trails

## Database Connection

The database connection is configured in `utils/postgres.js` with the following default settings:
- **Host**: localhost
- **Port**: 5432
- **Database**: adimas
- **User**: postgres
- **Password**: (empty)
- **SSL**: false

## Schema Structure

### 1. User Management & Authentication

#### Tables:
- **`users`** - Core user information with email-based authentication
- **`roles`** - Role definitions with JSON-based permissions
- **`user_roles`** - Many-to-many relationship between users and roles
- **`otp_tokens`** - OTP tokens for email verification and password reset

#### Key Features:
- Email-based authentication system
- Flexible role-based permissions using JSONB
- OTP system for secure email verification
- Row-level security policies for data protection

### 2. Product & Inventory Management

#### Tables:
- **`categories`** - Hierarchical product categories
- **`products`** - Product catalog with full metadata
- **`inventory`** - Real-time stock tracking with reservations
- **`stock_movements`** - Complete audit trail of all stock changes

#### Key Features:
- Hierarchical category structure
- Automatic inventory updates via triggers
- Stock reservation system for pending orders
- Comprehensive stock movement history
- Generated columns for available quantity calculation

### 3. Order Management

#### Tables:
- **`order_statuses`** - Configurable order status definitions
- **`orders`** - Order headers with customer and financial information
- **`order_items`** - Order line items with product snapshots

#### Key Features:
- Automatic order number generation
- Product information snapshots at time of order
- Flexible order status system with color coding
- Multi-currency support (default: IDR)

### 4. Shipping & Logistics

#### Tables:
- **`shipping_methods`** - Available shipping carriers and services
- **`shipping_addresses`** - Customer delivery addresses
- **`shipments`** - Tracking and delivery information

#### Key Features:
- Pre-configured Indonesian shipping carriers (JNE, TIKI, POS, J&T, SiCepat)
- Flexible cost calculation (base cost + per-kg pricing)
- Comprehensive tracking and delivery status
- Estimated delivery time ranges

### 5. Translation Management

#### Tables:
- **`translations`** - Existing translation table (preserved)
- **`translation_namespaces`** - Organization of translations by context
- **`translation_history`** - Audit trail for translation changes

#### Key Features:
- Namespace-based organization
- Complete change history tracking
- Support for multiple languages (English/Indonesian)

## Views

### 1. `product_inventory_summary`
Combines product information with current inventory status, including stock level indicators.

### 2. `order_summary`
Provides order overview with status information and item counts.

### 3. `user_roles_view`
Shows user information combined with their assigned roles and permissions.

## Functions & Triggers

### Automatic Timestamp Updates
- `update_updated_at_column()` - Updates `updated_at` timestamps automatically
- Applied to all tables with `updated_at` columns

### Inventory Management
- `update_inventory_on_movement()` - Automatically updates inventory when stock movements are recorded
- Ensures inventory accuracy and creates inventory records as needed

### Order Number Generation
- `generate_order_number()` - Creates unique order numbers in format: YYYYMMDD-XXXX

## Initial Data

The schema includes pre-populated data for:

### Roles:
- `super_admin` - Full system access
- `admin` - Most administrative functions
- `manager` - Product and order management
- `staff` - Basic operational access
- `viewer` - Read-only access

### Order Statuses:
- pending, processing, shipped, delivered, cancelled, refunded

### Shipping Methods:
- JNE (Regular, YES)
- TIKI (Regular, ONS)
- POS Indonesia
- J&T Express
- SiCepat

### Categories:
- Electronics, Clothing, Home & Garden, Sports, Books

### Translation Namespaces:
- common, products, orders, users, shipping, inventory, admin

## Security Features

### Row Level Security (RLS)
- Enabled on sensitive tables (`users`, `user_roles`, `otp_tokens`)
- Users can only access their own data unless they have admin privileges

### Data Integrity
- Comprehensive foreign key constraints
- Check constraints for data validation
- Unique constraints to prevent duplicates

## Performance Optimizations

### Indexes
- Strategic indexes on frequently queried columns
- Composite indexes for complex queries
- Unique indexes for business constraints

### Generated Columns
- `quantity_available` in inventory table calculated automatically
- Reduces query complexity and improves performance

## Usage Examples

### Creating a User with Role
```sql
-- Insert user
INSERT INTO users (email, first_name, last_name) 
VALUES ('<EMAIL>', 'Admin', 'User');

-- Assign admin role
INSERT INTO user_roles (user_id, role_id) 
SELECT u.id, r.id 
FROM users u, roles r 
WHERE u.email = '<EMAIL>' AND r.name = 'admin';
```

### Adding a Product with Inventory
```sql
-- Insert product
INSERT INTO products (name, slug, sku, price, category_id) 
VALUES ('Sample Product', 'sample-product', 'SKU001', 99.99, 
        (SELECT id FROM categories WHERE slug = 'electronics'));

-- Add initial stock
INSERT INTO stock_movements (product_id, movement_type, quantity, reason) 
VALUES ((SELECT id FROM products WHERE sku = 'SKU001'), 'in', 100, 'Initial stock');
```

### Creating an Order
```sql
-- Create order
INSERT INTO orders (order_number, customer_email, customer_name, status_id, total_amount) 
VALUES (generate_order_number(), '<EMAIL>', 'John Doe', 
        (SELECT id FROM order_statuses WHERE name = 'pending'), 199.98);

-- Add order items
INSERT INTO order_items (order_id, product_id, product_name, product_sku, quantity, unit_price, total_price) 
SELECT o.id, p.id, p.name, p.sku, 2, p.price, p.price * 2
FROM orders o, products p 
WHERE o.order_number = '20241220-0001' AND p.sku = 'SKU001';
```

## Maintenance

### Regular Tasks
1. Monitor inventory levels using `product_inventory_summary` view
2. Archive old stock movements periodically
3. Clean up expired OTP tokens
4. Review and update shipping method pricing

### Backup Recommendations
- Daily backups of the entire database
- Point-in-time recovery setup
- Regular backup restoration tests

## Next Steps

With the database schema in place, you can now:
1. Create API endpoints in your Next.js application
2. Implement authentication middleware
3. Build admin panel UI components
4. Set up data validation and business logic
5. Implement reporting and analytics features

The schema is designed to be flexible and extensible, allowing for future enhancements as your business requirements evolve.
