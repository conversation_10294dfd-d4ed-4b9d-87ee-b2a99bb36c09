-- =====================================================
-- ADIMAS ADMIN PANEL DATABASE SCHEMA
-- =====================================================
-- This schema supports a comprehensive admin panel for stock management
-- with user management, roles, products, orders, shipping, and translations

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- USER MANAGEMENT & ROLES
-- =====================================================

-- Roles table for access control
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '{}', -- Store permissions as JSON
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Users table with authentication fields
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255), -- For future password auth if needed
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User-role relationships (many-to-many)
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(id),
    UNIQUE(user_id, role_id)
);

-- OTP tokens for email verification
CREATE TABLE otp_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(10) NOT NULL,
    purpose VARCHAR(50) NOT NULL, -- 'email_verification', 'password_reset', etc.
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- STOCK MANAGEMENT
-- =====================================================

-- Categories table for product organization
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    image_url VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    short_description TEXT,
    sku VARCHAR(100) UNIQUE NOT NULL,
    barcode VARCHAR(100),
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    price DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    cost_price DECIMAL(12,2) DEFAULT 0.00,
    weight DECIMAL(8,3), -- in kg
    dimensions JSONB, -- {length, width, height}
    images JSONB DEFAULT '[]', -- Array of image URLs
    is_active BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    meta_title VARCHAR(255),
    meta_description TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Inventory/stock tracking
CREATE TABLE inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity_on_hand INTEGER NOT NULL DEFAULT 0,
    quantity_reserved INTEGER NOT NULL DEFAULT 0, -- Reserved for pending orders
    quantity_available INTEGER GENERATED ALWAYS AS (quantity_on_hand - quantity_reserved) STORED,
    reorder_level INTEGER DEFAULT 0,
    max_stock_level INTEGER,
    location VARCHAR(100), -- Warehouse location
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, location)
);

-- Stock movements/history
CREATE TABLE stock_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    movement_type VARCHAR(50) NOT NULL, -- 'in', 'out', 'adjustment', 'transfer'
    quantity INTEGER NOT NULL, -- Positive for in, negative for out
    reference_type VARCHAR(50), -- 'purchase', 'sale', 'adjustment', 'return'
    reference_id UUID, -- ID of related record (order, purchase, etc.)
    reason TEXT,
    cost_per_unit DECIMAL(12,2),
    location VARCHAR(100),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- ORDER MANAGEMENT
-- =====================================================

-- Order status lookup
CREATE TABLE order_statuses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true
);

-- Orders table
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20),
    status_id UUID NOT NULL REFERENCES order_statuses(id),
    subtotal DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    shipping_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'IDR',
    notes TEXT,
    internal_notes TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Order items/line items
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    product_name VARCHAR(255) NOT NULL, -- Snapshot at time of order
    product_sku VARCHAR(100) NOT NULL, -- Snapshot at time of order
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- SHIPPING
-- =====================================================

-- Shipping methods
CREATE TABLE shipping_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    carrier VARCHAR(100), -- JNE, TIKI, POS, etc.
    service_type VARCHAR(100), -- REG, YES, ONS, etc.
    base_cost DECIMAL(10,2) DEFAULT 0.00,
    cost_per_kg DECIMAL(10,2) DEFAULT 0.00,
    estimated_days_min INTEGER,
    estimated_days_max INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Shipping addresses
CREATE TABLE shipping_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    recipient_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address_line_1 VARCHAR(255) NOT NULL,
    address_line_2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state_province VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'Indonesia',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Shipping/tracking information
CREATE TABLE shipments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    shipping_method_id UUID REFERENCES shipping_methods(id),
    tracking_number VARCHAR(100),
    carrier VARCHAR(100),
    service_type VARCHAR(100),
    shipped_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    shipping_cost DECIMAL(10,2),
    weight DECIMAL(8,3),
    status VARCHAR(50) DEFAULT 'pending', -- pending, shipped, in_transit, delivered, returned
    notes TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- TRANSLATION MANAGEMENT EXTENSIONS
-- =====================================================
-- Note: translations table already exists, adding supporting tables

-- Translation namespaces for better organization
CREATE TABLE translation_namespaces (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Translation history for tracking changes
CREATE TABLE translation_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    translation_id UUID NOT NULL, -- References translations.id
    field_name VARCHAR(50) NOT NULL, -- 'lang_en', 'lang_id', etc.
    old_value TEXT,
    new_value TEXT,
    changed_by UUID REFERENCES users(id),
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User management indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX idx_otp_tokens_user_id ON otp_tokens(user_id);
CREATE INDEX idx_otp_tokens_token ON otp_tokens(token);
CREATE INDEX idx_otp_tokens_expires_at ON otp_tokens(expires_at);

-- Product and inventory indexes
CREATE INDEX idx_categories_parent_id ON categories(parent_id);
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_inventory_product_id ON inventory(product_id);
CREATE INDEX idx_stock_movements_product_id ON stock_movements(product_id);
CREATE INDEX idx_stock_movements_created_at ON stock_movements(created_at);

-- Order management indexes
CREATE INDEX idx_orders_status_id ON orders(status_id);
CREATE INDEX idx_orders_customer_email ON orders(customer_email);
CREATE INDEX idx_orders_order_number ON orders(order_number);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- Shipping indexes
CREATE INDEX idx_shipping_addresses_order_id ON shipping_addresses(order_id);
CREATE INDEX idx_shipments_order_id ON shipments(order_id);
CREATE INDEX idx_shipments_tracking_number ON shipments(tracking_number);
CREATE INDEX idx_shipments_status ON shipments(status);

-- Translation indexes
CREATE INDEX idx_translation_history_translation_id ON translation_history(translation_id);
CREATE INDEX idx_translation_history_changed_by ON translation_history(changed_by);
CREATE INDEX idx_translation_history_changed_at ON translation_history(changed_at);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_shipping_methods_updated_at BEFORE UPDATE ON shipping_methods FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_shipments_updated_at BEFORE UPDATE ON shipments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default roles
INSERT INTO roles (name, description, permissions) VALUES
('super_admin', 'Super Administrator with full access', '{"all": true}'),
('admin', 'Administrator with most permissions', '{"users": true, "products": true, "orders": true, "inventory": true, "shipping": true, "translations": true}'),
('manager', 'Manager with limited admin access', '{"products": true, "orders": true, "inventory": true, "shipping": true}'),
('staff', 'Staff member with basic access', '{"orders": true, "inventory": {"read": true}}'),
('viewer', 'Read-only access', '{"orders": {"read": true}, "products": {"read": true}, "inventory": {"read": true}}');

-- Insert default order statuses
INSERT INTO order_statuses (name, description, color, sort_order) VALUES
('pending', 'Order received, awaiting processing', '#FFA500', 1),
('processing', 'Order is being prepared', '#2196F3', 2),
('shipped', 'Order has been shipped', '#9C27B0', 3),
('delivered', 'Order has been delivered', '#4CAF50', 4),
('cancelled', 'Order has been cancelled', '#F44336', 5),
('refunded', 'Order has been refunded', '#607D8B', 6);

-- Insert default shipping methods (Indonesian carriers)
INSERT INTO shipping_methods (name, description, carrier, service_type, base_cost, cost_per_kg, estimated_days_min, estimated_days_max) VALUES
('JNE Regular', 'JNE Regular Service', 'JNE', 'REG', 9000.00, 2000.00, 2, 4),
('JNE YES', 'JNE Express Service', 'JNE', 'YES', 15000.00, 3000.00, 1, 2),
('TIKI Regular', 'TIKI Regular Service', 'TIKI', 'REG', 8500.00, 1800.00, 2, 5),
('TIKI ONS', 'TIKI Overnight Service', 'TIKI', 'ONS', 18000.00, 3500.00, 1, 1),
('POS Indonesia', 'POS Indonesia Regular', 'POS', 'REG', 7000.00, 1500.00, 3, 7),
('J&T Express', 'J&T Express Regular', 'J&T', 'REG', 8000.00, 1700.00, 2, 4),
('SiCepat', 'SiCepat Regular', 'SICEPAT', 'REG', 8500.00, 1900.00, 2, 4);

-- Insert default translation namespaces
INSERT INTO translation_namespaces (name, description) VALUES
('common', 'Common UI elements and messages'),
('products', 'Product-related translations'),
('orders', 'Order management translations'),
('users', 'User management translations'),
('shipping', 'Shipping and logistics translations'),
('inventory', 'Inventory management translations'),
('admin', 'Admin panel specific translations');

-- Insert sample root category
INSERT INTO categories (name, slug, description) VALUES
('Electronics', 'electronics', 'Electronic products and accessories'),
('Clothing', 'clothing', 'Clothing and fashion items'),
('Home & Garden', 'home-garden', 'Home and garden products'),
('Sports', 'sports', 'Sports and outdoor equipment'),
('Books', 'books', 'Books and educational materials');

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for product inventory summary
CREATE VIEW product_inventory_summary AS
SELECT
    p.id,
    p.name,
    p.sku,
    p.price,
    c.name as category_name,
    i.quantity_on_hand,
    i.quantity_reserved,
    i.quantity_available,
    i.reorder_level,
    CASE
        WHEN i.quantity_available <= i.reorder_level THEN 'low_stock'
        WHEN i.quantity_available = 0 THEN 'out_of_stock'
        ELSE 'in_stock'
    END as stock_status,
    p.is_active,
    p.created_at,
    p.updated_at
FROM products p
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN inventory i ON p.id = i.product_id;

-- View for order summary with status
CREATE VIEW order_summary AS
SELECT
    o.id,
    o.order_number,
    o.customer_name,
    o.customer_email,
    os.name as status_name,
    os.color as status_color,
    o.total_amount,
    o.currency,
    COUNT(oi.id) as item_count,
    o.created_at,
    o.updated_at
FROM orders o
LEFT JOIN order_statuses os ON o.status_id = os.id
LEFT JOIN order_items oi ON o.id = oi.order_id
GROUP BY o.id, os.name, os.color;

-- View for user roles
CREATE VIEW user_roles_view AS
SELECT
    u.id as user_id,
    u.email,
    u.first_name,
    u.last_name,
    u.is_active,
    r.name as role_name,
    r.permissions,
    ur.assigned_at
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id;

-- =====================================================
-- FUNCTIONS FOR BUSINESS LOGIC
-- =====================================================

-- Function to update inventory after stock movement
CREATE OR REPLACE FUNCTION update_inventory_on_movement()
RETURNS TRIGGER AS $$
BEGIN
    -- Update inventory quantity based on movement
    UPDATE inventory
    SET quantity_on_hand = quantity_on_hand + NEW.quantity,
        updated_at = CURRENT_TIMESTAMP
    WHERE product_id = NEW.product_id
    AND (location = NEW.location OR (location IS NULL AND NEW.location IS NULL));

    -- If no inventory record exists, create one
    IF NOT FOUND THEN
        INSERT INTO inventory (product_id, quantity_on_hand, location)
        VALUES (NEW.product_id, GREATEST(NEW.quantity, 0), NEW.location);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update inventory on stock movements
CREATE TRIGGER trigger_update_inventory_on_movement
    AFTER INSERT ON stock_movements
    FOR EACH ROW
    EXECUTE FUNCTION update_inventory_on_movement();

-- Function to generate order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    counter INTEGER;
BEGIN
    -- Get current date in YYYYMMDD format
    SELECT TO_CHAR(CURRENT_DATE, 'YYYYMMDD') INTO new_number;

    -- Get count of orders created today
    SELECT COUNT(*) + 1 INTO counter
    FROM orders
    WHERE DATE(created_at) = CURRENT_DATE;

    -- Format: YYYYMMDD-XXXX (e.g., 20241220-0001)
    new_number := new_number || '-' || LPAD(counter::TEXT, 4, '0');

    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SECURITY POLICIES (Row Level Security)
-- =====================================================

-- Enable RLS on sensitive tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE otp_tokens ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be expanded based on specific requirements)
-- Users can only see their own data unless they have admin role
CREATE POLICY users_own_data ON users
    FOR ALL
    USING (id = current_setting('app.current_user_id')::UUID OR
           EXISTS (SELECT 1 FROM user_roles ur JOIN roles r ON ur.role_id = r.id
                   WHERE ur.user_id = current_setting('app.current_user_id')::UUID
                   AND r.name IN ('super_admin', 'admin')));

-- =====================================================
-- SCHEMA CREATION COMPLETE
-- =====================================================

-- Display summary of created objects
SELECT 'Schema creation completed successfully!' as status;
