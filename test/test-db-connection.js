#!/usr/bin/env node

/**
 * Database Connection Test Script
 * 
 * This script tests the database connection and verifies the schema
 * was created correctly with all tables, relationships, and initial data.
 */

import { testConnection, query } from '../utils/postgres.js';

async function testDatabaseSchema() {
    console.log('🔍 Testing database connection and schema...\n');

    try {
        // Test basic connection
        console.log('1. Testing database connection...');
        const connectionResult = await testConnection();
        if (!connectionResult) {
            throw new Error('Database connection failed');
        }
        console.log('✅ Database connection successful\n');

        // Test table creation
        console.log('2. Verifying table creation...');
        const tablesResult = await query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name
        `);
        
        const expectedTables = [
            'categories', 'inventory', 'order_items', 'order_statuses', 'orders',
            'otp_tokens', 'products', 'roles', 'shipments', 'shipping_addresses',
            'shipping_methods', 'stock_movements', 'translation_history',
            'translation_namespaces', 'translations', 'user_roles', 'users'
        ];

        const actualTables = tablesResult.rows.map(row => row.table_name);
        const missingTables = expectedTables.filter(table => !actualTables.includes(table));
        
        if (missingTables.length > 0) {
            throw new Error(`Missing tables: ${missingTables.join(', ')}`);
        }
        
        console.log(`✅ All ${expectedTables.length} tables created successfully`);
        console.log(`   Tables: ${actualTables.join(', ')}\n`);

        // Test views creation
        console.log('3. Verifying views creation...');
        const viewsResult = await query(`
            SELECT table_name 
            FROM information_schema.views 
            WHERE table_schema = 'public'
            ORDER BY table_name
        `);
        
        const expectedViews = ['order_summary', 'product_inventory_summary', 'user_roles_view'];
        const actualViews = viewsResult.rows.map(row => row.table_name);
        
        console.log(`✅ ${actualViews.length} views created: ${actualViews.join(', ')}\n`);

        // Test initial data
        console.log('4. Verifying initial data...');
        
        // Check roles
        const rolesResult = await query('SELECT COUNT(*) as count FROM roles');
        console.log(`✅ Roles table: ${rolesResult.rows[0].count} roles inserted`);
        
        // Check order statuses
        const statusResult = await query('SELECT COUNT(*) as count FROM order_statuses');
        console.log(`✅ Order statuses: ${statusResult.rows[0].count} statuses inserted`);
        
        // Check shipping methods
        const shippingResult = await query('SELECT COUNT(*) as count FROM shipping_methods');
        console.log(`✅ Shipping methods: ${shippingResult.rows[0].count} methods inserted`);
        
        // Check categories
        const categoriesResult = await query('SELECT COUNT(*) as count FROM categories');
        console.log(`✅ Categories: ${categoriesResult.rows[0].count} categories inserted`);
        
        // Check translation namespaces
        const namespacesResult = await query('SELECT COUNT(*) as count FROM translation_namespaces');
        console.log(`✅ Translation namespaces: ${namespacesResult.rows[0].count} namespaces inserted\n`);

        // Test foreign key relationships
        console.log('5. Testing foreign key relationships...');
        
        // Test user_roles relationship
        const fkTest = await query(`
            SELECT COUNT(*) as count 
            FROM information_schema.table_constraints 
            WHERE constraint_type = 'FOREIGN KEY' 
            AND table_schema = 'public'
        `);
        console.log(`✅ Foreign key constraints: ${fkTest.rows[0].count} constraints created\n`);

        // Test functions and triggers
        console.log('6. Verifying functions and triggers...');
        const functionsResult = await query(`
            SELECT routine_name 
            FROM information_schema.routines 
            WHERE routine_schema = 'public' 
            AND routine_type = 'FUNCTION'
            ORDER BY routine_name
        `);
        
        const functions = functionsResult.rows.map(row => row.routine_name);
        console.log(`✅ Functions created: ${functions.join(', ')}\n`);

        // Test sample queries on views
        console.log('7. Testing view functionality...');
        
        const inventoryView = await query('SELECT COUNT(*) as count FROM product_inventory_summary');
        console.log(`✅ Product inventory summary view: ${inventoryView.rows[0].count} records`);
        
        const orderView = await query('SELECT COUNT(*) as count FROM order_summary');
        console.log(`✅ Order summary view: ${orderView.rows[0].count} records`);
        
        const userRolesView = await query('SELECT COUNT(*) as count FROM user_roles_view');
        console.log(`✅ User roles view: ${userRolesView.rows[0].count} records\n`);

        console.log('🎉 Database schema verification completed successfully!');
        console.log('📊 Schema Summary:');
        console.log(`   - ${expectedTables.length} tables created`);
        console.log(`   - ${actualViews.length} views created`);
        console.log(`   - ${functions.length} functions created`);
        console.log(`   - ${fkTest.rows[0].count} foreign key constraints`);
        console.log(`   - Initial data populated in all lookup tables`);
        console.log('\n✨ Your admin panel database is ready for use!');

    } catch (error) {
        console.error('❌ Database schema test failed:', error.message);
        process.exit(1);
    }
}

// Run the test
testDatabaseSchema();
