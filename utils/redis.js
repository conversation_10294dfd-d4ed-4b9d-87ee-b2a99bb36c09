import Redis from "ioredis";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

/**
 * Redis Connection Configuration
 *
 * Optimized for high-performance caching and pub/sub operations.
 * Uses lazy connection to improve startup time and connection pooling.
 */
const redisConfig = {
  host: process.env.REDIS_HOST || "localhost",
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB) || 9,

  // Connection resilience settings
  retryDelayOnFailover: 100, // Retry delay during failover (ms)
  maxRetriesPerRequest: 3, // Max retries per request before failing
  lazyConnect: true, // Connect only when first command is issued
};

/**
 * Redis Instance Creation
 *
 * Creates separate Redis instances for different use cases:
 * - redis: General caching and data operations
 * - redisPub: Publishing messages to channels
 * - redisSub: Subscribing to channels (dedicated connection)
 */
export const redis = new Redis(redisConfig); // Main cache and operations
export const redisPub = new Redis(redisConfig); // Publisher for pub/sub
export const redisSub = new Redis(redisConfig); // Subscriber for pub/sub

/**
 * Redis Connection Event Handlers
 *
 * Provides comprehensive monitoring and logging for all Redis connections.
 * Helps with debugging connection issues and monitoring system health.
 */

// Main Redis instance event handlers
redis.on("connect", () => {
  console.log("✅ Redis connected successfully");
});

redis.on("error", (err) => {
  console.error("❌ Redis connection error:", err.message);
});

redis.on("ready", () => {
  console.log("✅ Redis ready for operations");
});

// Publisher instance event handlers
redisPub.on("connect", () => {
  console.log("✅ Redis Publisher connected");
});

redisPub.on("error", (err) => {
  console.error("❌ Redis Publisher error:", err.message);
});

// Subscriber instance event handlers
redisSub.on("connect", () => {
  console.log("✅ Redis Subscriber connected");
});

redisSub.on("error", (err) => {
  console.error("❌ Redis Subscriber error:", err.message);
});

/**
 * Redis Connection Test Function
 *
 * Tests Redis connectivity using a ping command.
 * Used during application startup and health checks.
 *
 * @async
 * @returns {boolean} True if Redis is responsive, false otherwise
 */
export const testRedisConnection = async () => {
  try {
    await redis.ping();
    console.log("✅ Redis ping successful");

    return true;
  } catch (err) {
    console.error("❌ Redis ping failed:", err.message);

    return false;
  }
};

/**
 * Cache Helper Functions
 *
 * Provides a high-level interface for common caching operations
 * with automatic JSON serialization/deserialization and error handling.
 */
export const cache = {
  /**
   * Get value from cache
   *
   * @param {string} key - Cache key
   * @returns {*} Parsed value or null if not found/error
   */
  get: async (key) => {
    try {
      const value = await redis.get(key);

      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error("Cache get error:", error);

      return null; // Return null on error to prevent app crashes
    }
  },

  /**
   * Set value in cache with TTL
   *
   * @param {string} key - Cache key
   * @param {*} value - Value to cache (will be JSON stringified)
   * @param {number} ttl - Time to live in seconds (default from env or 300)
   * @returns {boolean} True if successful, false otherwise
   */
  set: async (key, value, ttl = parseInt(process.env.CACHE_TTL) || 300) => {
    try {
      await redis.set(key, JSON.stringify(value), "EX", ttl);

      return true;
    } catch (error) {
      console.error("Cache set error:", error);

      return false;
    }
  },

  /**
   * Delete value from cache
   *
   * @param {string} key - Cache key to delete
   * @returns {boolean} True if successful, false otherwise
   */
  del: async (key) => {
    try {
      await redis.del(key);

      return true;
    } catch (error) {
      console.error("Cache delete error:", error);

      return false;
    }
  },

  /**
   * Check if key exists in cache
   *
   * @param {string} key - Cache key to check
   * @returns {boolean} True if key exists, false otherwise
   */
  exists: async (key) => {
    try {
      return await redis.exists(key);
    } catch (error) {
      console.error("Cache exists error:", error);

      return false;
    }
  },

  /**
   * Increment counter with automatic expiration
   *
   * Used for rate limiting and usage tracking.
   *
   * @param {string} key - Counter key
   * @param {number} ttl - Time to live in seconds (default 1 hour)
   * @returns {number|null} New counter value or null on error
   */
  incr: async (key, ttl = 3600) => {
    try {
      const multi = redis.multi();

      multi.incr(key);
      multi.expire(key, ttl);
      const results = await multi.exec();

      return results[0][1]; // Return the incremented value
    } catch (error) {
      console.error("Cache increment error:", error);

      return null;
    }
  },

  // List operations for storing recent messages
  // Push item to the front of a list and trim to maxLength
  lpushAndTrim: async (key, value, maxLength = 100) => {
    try {
      const multi = redis.multi();

      multi.lpush(key, JSON.stringify(value));
      multi.ltrim(key, 0, maxLength - 1); // Keep only the last maxLength items
      await multi.exec();

      return true;
    } catch (error) {
      console.error("List push and trim error:", error);

      return false;
    }
  },

  // Get range of items from a list
  lrange: async (key, start = 0, stop = -1) => {
    try {
      const items = await redis.lrange(key, start, stop);

      return items.map((item) => {
        try {
          return JSON.parse(item);
        } catch (parseError) {
          console.error("JSON parse error for list item:", parseError);

          return item; // Return as string if JSON parse fails
        }
      });
    } catch (error) {
      console.error("List range error:", error);

      return [];
    }
  },

  // Get list length
  llen: async (key) => {
    try {
      return await redis.llen(key);
    } catch (error) {
      console.error("List length error:", error);

      return 0;
    }
  },
};

// Pub/Sub helper functions
export const pubsub = {
  // Publish message to channel
  publish: async (channel, message) => {
    try {
      const messageStr =
        typeof message === "string" ? message : JSON.stringify(message);

      await redisPub.publish(channel, messageStr);

      return true;
    } catch (error) {
      console.error("Publish error:", error);

      return false;
    }
  },

  // Subscribe to channel
  subscribe: async (channel, callback) => {
    try {
      await redisSub.subscribe(channel);
      redisSub.on("message", (receivedChannel, message) => {
        if (receivedChannel === channel) {
          try {
            const parsedMessage = JSON.parse(message);

            callback(parsedMessage);
          } catch (error) {
            callback(message); // If not JSON, return as string
          }
        }
      });

      return true;
    } catch (error) {
      console.error("Subscribe error:", error);

      return false;
    }
  },

  // Unsubscribe from channel
  unsubscribe: async (channel) => {
    try {
      await redisSub.unsubscribe(channel);

      return true;
    } catch (error) {
      console.error("Unsubscribe error:", error);

      return false;
    }
  },
};

// Track if Redis connections have been closed
let redisClosed = false;

// Close Redis connections
export const closeRedis = async () => {
  if (redisClosed) {
    console.log("Redis connections already closed, skipping...");

    return;
  }

  try {
    await redis.quit();
    await redisPub.quit();
    await redisSub.quit();
    redisClosed = true;
    console.log("Redis connections closed");
  } catch (error) {
    console.error("Error closing Redis connections:", error.message);
    redisClosed = true; // Mark as closed even if there was an error
  }
};

export default redis;
