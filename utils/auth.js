/**
 * Authentication Utilities
 * 
 * JWT token management, validation, and authentication helpers
 * using the jose library for secure JWT operations.
 */

import { SignJWT, jwtVerify } from 'jose';
import { authConfig } from '@/config/auth';

// Convert string secret to Uint8Array for jose
const getSecretKey = () => {
  return new TextEncoder().encode(authConfig.jwt.secret);
};

/**
 * Generate JWT token for authenticated user
 * @param {Object} payload - User data to include in token
 * @returns {Promise<string>} JWT token
 */
export async function generateToken(payload) {
  try {
    const secret = getSecretKey();
    
    const token = await new SignJWT(payload)
      .setProtectedHeader({ alg: authConfig.jwt.algorithm })
      .setIssuedAt()
      .setIssuer(authConfig.jwt.issuer)
      .setAudience(authConfig.jwt.audience)
      .setExpirationTime(authConfig.jwt.expiresIn)
      .sign(secret);

    return token;
  } catch (error) {
    console.error('Error generating JWT token:', error);
    throw new Error('Failed to generate authentication token');
  }
}

/**
 * Generate refresh token with longer expiry
 * @param {Object} payload - User data to include in token
 * @returns {Promise<string>} Refresh JWT token
 */
export async function generateRefreshToken(payload) {
  try {
    const secret = getSecretKey();
    
    const token = await new SignJWT({ ...payload, type: 'refresh' })
      .setProtectedHeader({ alg: authConfig.jwt.algorithm })
      .setIssuedAt()
      .setIssuer(authConfig.jwt.issuer)
      .setAudience(authConfig.jwt.audience)
      .setExpirationTime(authConfig.jwt.refreshExpiresIn)
      .sign(secret);

    return token;
  } catch (error) {
    console.error('Error generating refresh token:', error);
    throw new Error('Failed to generate refresh token');
  }
}

/**
 * Verify and decode JWT token
 * @param {string} token - JWT token to verify
 * @returns {Promise<Object>} Decoded token payload
 */
export async function verifyToken(token) {
  try {
    if (!token) {
      throw new Error('No token provided');
    }

    const secret = getSecretKey();
    
    const { payload } = await jwtVerify(token, secret, {
      issuer: authConfig.jwt.issuer,
      audience: authConfig.jwt.audience,
    });

    return payload;
  } catch (error) {
    console.error('Token verification failed:', error.message);
    throw new Error('Invalid or expired token');
  }
}

/**
 * Extract token from request headers or cookies
 * @param {Request} request - Next.js request object
 * @returns {string|null} JWT token or null
 */
export function extractToken(request) {
  // Try Authorization header first
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Try cookie
  const cookies = request.headers.get('cookie');
  if (cookies) {
    const tokenCookie = cookies
      .split(';')
      .find(c => c.trim().startsWith(`${authConfig.cookies.tokenName}=`));
    
    if (tokenCookie) {
      return tokenCookie.split('=')[1];
    }
  }

  return null;
}

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
export function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return false;
  }

  return authConfig.validation.email.pattern.test(email) && 
         email.length <= authConfig.validation.email.maxLength;
}

/**
 * Validate OTP format
 * @param {string} otp - OTP to validate
 * @returns {boolean} True if valid OTP
 */
export function validateOtp(otp) {
  if (!otp || typeof otp !== 'string') {
    return false;
  }

  return authConfig.validation.otp.pattern.test(otp);
}

/**
 * Create secure response with token cookies
 * @param {Object} data - Response data
 * @param {string} token - JWT token
 * @param {string} refreshToken - Refresh token
 * @returns {Response} Next.js Response with secure cookies
 */
export function createAuthResponse(data, token, refreshToken) {
  const response = new Response(JSON.stringify(data), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      ...authConfig.security.headers
    }
  });

  // Set secure HTTP-only cookies
  const cookieOptions = authConfig.cookies.options;
  
  response.headers.append(
    'Set-Cookie',
    `${authConfig.cookies.tokenName}=${token}; ${Object.entries(cookieOptions)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ')}`
  );

  if (refreshToken) {
    response.headers.append(
      'Set-Cookie',
      `${authConfig.cookies.refreshTokenName}=${refreshToken}; ${Object.entries(cookieOptions)
        .map(([key, value]) => `${key}=${value}`)
        .join('; ')}`
    );
  }

  return response;
}

/**
 * Create logout response that clears auth cookies
 * @param {Object} data - Response data
 * @returns {Response} Next.js Response with cleared cookies
 */
export function createLogoutResponse(data = { success: true, message: 'Logged out successfully' }) {
  const response = new Response(JSON.stringify(data), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      ...authConfig.security.headers
    }
  });

  // Clear auth cookies
  response.headers.append(
    'Set-Cookie',
    `${authConfig.cookies.tokenName}=; Max-Age=0; Path=/; HttpOnly; SameSite=strict`
  );

  response.headers.append(
    'Set-Cookie',
    `${authConfig.cookies.refreshTokenName}=; Max-Age=0; Path=/; HttpOnly; SameSite=strict`
  );

  return response;
}

/**
 * Get current user from token
 * @param {Request} request - Next.js request object
 * @returns {Promise<Object|null>} User data or null
 */
export async function getCurrentUser(request) {
  try {
    const token = extractToken(request);
    if (!token) {
      return null;
    }

    const payload = await verifyToken(token);
    return payload;
  } catch (error) {
    return null;
  }
}

/**
 * Check if user has required role
 * @param {Object} user - User object from token
 * @param {string|Array} requiredRoles - Required role(s)
 * @returns {boolean} True if user has required role
 */
export function hasRole(user, requiredRoles) {
  if (!user || !user.roles) {
    return false;
  }

  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  return roles.some(role => user.roles.includes(role));
}

/**
 * Create error response with proper headers
 * @param {string} message - Error message
 * @param {number} status - HTTP status code
 * @returns {Response} Error response
 */
export function createErrorResponse(message, status = 400) {
  return new Response(
    JSON.stringify({ 
      success: false, 
      error: message 
    }), 
    {
      status,
      headers: {
        'Content-Type': 'application/json',
        ...authConfig.security.headers
      }
    }
  );
}
