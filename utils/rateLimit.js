/**
 * Rate Limiting Utilities
 * 
 * In-memory rate limiting for API endpoints to prevent abuse.
 * Uses Redis for production scalability when available.
 */

import { authConfig } from '@/config/auth';
import { cache } from '@/utils/redis';

// In-memory store for development (fallback when Redis is not available)
const memoryStore = new Map();

/**
 * Clean up expired entries from memory store
 */
function cleanupMemoryStore() {
  const now = Date.now();
  for (const [key, data] of memoryStore.entries()) {
    if (now > data.resetTime) {
      memoryStore.delete(key);
    }
  }
}

/**
 * Get client identifier from request
 * @param {Request} request - Next.js request object
 * @returns {string} Client identifier (IP + User-Agent hash)
 */
function getClientId(request) {
  // Get IP address
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0].trim() : 
             request.headers.get('x-real-ip') || 
             'unknown';

  // Get User-Agent for additional uniqueness
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  // Create a simple hash of user agent to avoid storing full string
  const uaHash = userAgent.split('').reduce((hash, char) => {
    return ((hash << 5) - hash) + char.charCodeAt(0);
  }, 0);

  return `${ip}:${Math.abs(uaHash)}`;
}

/**
 * Check rate limit for a client
 * @param {string} key - Rate limit key
 * @param {Object} config - Rate limit configuration
 * @returns {Promise<Object>} Rate limit status
 */
async function checkRateLimit(key, config) {
  const now = Date.now();
  const windowStart = now - config.windowMs;

  try {
    // Try Redis first
    const redisKey = `ratelimit:${key}`;
    const existing = await cache.get(redisKey);

    if (existing) {
      const { count, resetTime } = existing;
      
      if (now < resetTime) {
        // Within current window
        if (count >= config.maxRequests) {
          return {
            allowed: false,
            count,
            remaining: 0,
            resetTime,
            retryAfter: Math.ceil((resetTime - now) / 1000)
          };
        }

        // Increment count
        const newCount = count + 1;
        await cache.set(redisKey, { count: newCount, resetTime }, Math.ceil(config.windowMs / 1000));
        
        return {
          allowed: true,
          count: newCount,
          remaining: config.maxRequests - newCount,
          resetTime,
          retryAfter: 0
        };
      } else {
        // Window expired, start new window
        const resetTime = now + config.windowMs;
        await cache.set(redisKey, { count: 1, resetTime }, Math.ceil(config.windowMs / 1000));
        
        return {
          allowed: true,
          count: 1,
          remaining: config.maxRequests - 1,
          resetTime,
          retryAfter: 0
        };
      }
    } else {
      // First request
      const resetTime = now + config.windowMs;
      await cache.set(redisKey, { count: 1, resetTime }, Math.ceil(config.windowMs / 1000));
      
      return {
        allowed: true,
        count: 1,
        remaining: config.maxRequests - 1,
        resetTime,
        retryAfter: 0
      };
    }
  } catch (redisError) {
    // Fallback to memory store
    console.warn('Redis unavailable, using memory store for rate limiting:', redisError.message);
    
    // Clean up expired entries periodically
    if (Math.random() < 0.1) { // 10% chance to cleanup
      cleanupMemoryStore();
    }

    const existing = memoryStore.get(key);
    
    if (existing) {
      const { count, resetTime } = existing;
      
      if (now < resetTime) {
        // Within current window
        if (count >= config.maxRequests) {
          return {
            allowed: false,
            count,
            remaining: 0,
            resetTime,
            retryAfter: Math.ceil((resetTime - now) / 1000)
          };
        }

        // Increment count
        const newCount = count + 1;
        memoryStore.set(key, { count: newCount, resetTime });
        
        return {
          allowed: true,
          count: newCount,
          remaining: config.maxRequests - newCount,
          resetTime,
          retryAfter: 0
        };
      } else {
        // Window expired, start new window
        const resetTime = now + config.windowMs;
        memoryStore.set(key, { count: 1, resetTime });
        
        return {
          allowed: true,
          count: 1,
          remaining: config.maxRequests - 1,
          resetTime,
          retryAfter: 0
        };
      }
    } else {
      // First request
      const resetTime = now + config.windowMs;
      memoryStore.set(key, { count: 1, resetTime });
      
      return {
        allowed: true,
        count: 1,
        remaining: config.maxRequests - 1,
        resetTime,
        retryAfter: 0
      };
    }
  }
}

/**
 * Rate limiting middleware for API routes
 * @param {Request} request - Next.js request object
 * @param {string} type - Rate limit type ('general', 'email', 'otp')
 * @returns {Promise<Object>} Rate limit result
 */
export async function rateLimit(request, type = 'general') {
  const config = authConfig.rateLimit[type] || authConfig.rateLimit.general;
  const clientId = getClientId(request);
  const key = `${type}:${clientId}`;

  return await checkRateLimit(key, config);
}

/**
 * Email-specific rate limiting (more restrictive)
 * @param {Request} request - Next.js request object
 * @param {string} email - Email address for additional limiting
 * @returns {Promise<Object>} Rate limit result
 */
export async function emailRateLimit(request, email) {
  const clientId = getClientId(request);
  
  // Check both IP-based and email-based limits
  const ipLimit = await checkRateLimit(
    `email:ip:${clientId}`, 
    authConfig.rateLimit.email
  );
  
  const emailLimit = await checkRateLimit(
    `email:addr:${email}`, 
    authConfig.rateLimit.email
  );

  // Return the most restrictive limit
  if (!ipLimit.allowed || !emailLimit.allowed) {
    return {
      allowed: false,
      count: Math.max(ipLimit.count, emailLimit.count),
      remaining: 0,
      resetTime: Math.max(ipLimit.resetTime, emailLimit.resetTime),
      retryAfter: Math.max(ipLimit.retryAfter, emailLimit.retryAfter),
      limitType: !ipLimit.allowed ? 'IP' : 'Email'
    };
  }

  return {
    allowed: true,
    count: Math.max(ipLimit.count, emailLimit.count),
    remaining: Math.min(ipLimit.remaining, emailLimit.remaining),
    resetTime: Math.max(ipLimit.resetTime, emailLimit.resetTime),
    retryAfter: 0
  };
}

/**
 * Create rate limit response headers
 * @param {Object} limitResult - Rate limit result
 * @returns {Object} Headers object
 */
export function createRateLimitHeaders(limitResult) {
  return {
    'X-RateLimit-Limit': limitResult.count + limitResult.remaining,
    'X-RateLimit-Remaining': limitResult.remaining,
    'X-RateLimit-Reset': new Date(limitResult.resetTime).toISOString(),
    ...(limitResult.retryAfter > 0 && {
      'Retry-After': limitResult.retryAfter
    })
  };
}

/**
 * Create rate limit exceeded response
 * @param {Object} limitResult - Rate limit result
 * @param {string} message - Custom error message
 * @returns {Response} Rate limit error response
 */
export function createRateLimitResponse(limitResult, message) {
  return new Response(
    JSON.stringify({
      success: false,
      error: message || 'Rate limit exceeded',
      retryAfter: limitResult.retryAfter
    }),
    {
      status: 429,
      headers: {
        'Content-Type': 'application/json',
        ...createRateLimitHeaders(limitResult)
      }
    }
  );
}
