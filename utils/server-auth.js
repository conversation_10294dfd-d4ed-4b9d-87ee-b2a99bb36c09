/**
 * Server-Side Authentication Utilities
 * 
 * Utilities for handling authentication on the server-side in Next.js
 * App Router. These functions run on the server and provide better
 * performance than client-side auth checks.
 */

import { cookies } from 'next/headers';
import { verifyToken } from './auth';
import { authConfig } from '@/config/auth';

/**
 * Get current user from server-side cookies
 * @returns {Promise<Object|null>} User data or null if not authenticated
 */
export async function getCurrentUserServer() {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(authConfig.cookies.tokenName);

    if (!token || !token.value) {
      return null;
    }

    // Verify the token
    const payload = await verifyToken(token.value);
    
    return {
      id: payload.id,
      email: payload.email,
      name: payload.name,
      roles: payload.roles,
      loginAt: payload.loginAt
    };
  } catch (error) {
    console.error('Server auth check failed:', error.message);
    return null;
  }
}

/**
 * Check if user is authenticated on server-side
 * @returns {Promise<boolean>} True if authenticated
 */
export async function isAuthenticatedServer() {
  const user = await getCurrentUserServer();
  return !!user;
}

/**
 * Check if user has required role on server-side
 * @param {string|Array} requiredRoles - Required role(s)
 * @returns {Promise<boolean>} True if user has required role
 */
export async function hasRoleServer(requiredRoles) {
  const user = await getCurrentUserServer();
  
  if (!user || !user.roles) {
    return false;
  }

  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  return roles.some(role => user.roles.includes(role));
}

/**
 * Check if user is admin on server-side
 * @returns {Promise<boolean>} True if user is admin
 */
export async function isAdminServer() {
  return await hasRoleServer(['super_admin', 'admin']);
}

/**
 * Require authentication for a page (redirect if not authenticated)
 * @param {string} redirectTo - Where to redirect if not authenticated
 * @returns {Promise<Object|null>} User data or null (with redirect)
 */
export async function requireAuth(redirectTo = '/') {
  const user = await getCurrentUserServer();
  
  if (!user) {
    // In App Router, we can't directly redirect from server components
    // The calling component should handle the redirect
    return null;
  }
  
  return user;
}

/**
 * Require specific role for a page
 * @param {string|Array} requiredRoles - Required role(s)
 * @param {string} redirectTo - Where to redirect if unauthorized
 * @returns {Promise<Object|null>} User data or null (with redirect)
 */
export async function requireRole(requiredRoles, redirectTo = '/') {
  const user = await getCurrentUserServer();
  
  if (!user) {
    return null;
  }
  
  const hasRequiredRole = await hasRoleServer(requiredRoles);
  if (!hasRequiredRole) {
    return null;
  }
  
  return user;
}

/**
 * Get authentication status for client components
 * This can be passed as props to client components to avoid hydration issues
 * @returns {Promise<Object>} Auth status object
 */
export async function getAuthStatus() {
  const user = await getCurrentUserServer();
  
  return {
    isAuthenticated: !!user,
    user: user,
    loading: false // Server-side is never loading
  };
}
