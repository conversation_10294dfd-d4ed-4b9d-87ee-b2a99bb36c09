"use client";

import { useState, useEffect, useCallback } from 'react';
import Cookies from 'js-cookie';
import { authConfig } from '@/config/auth';

/**
 * Authentication Hook
 * 
 * Manages authentication state, token validation, and user data.
 * Provides methods for login, logout, and checking auth status.
 */
export function useAuth() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is authenticated
  const checkAuth = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if token exists in cookies
      const token = Cookies.get(authConfig.cookies.tokenName);
      
      if (!token) {
        setUser(null);
        setLoading(false);
        return false;
      }

      // Validate token with server
      const response = await fetch('/api/auth/me', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        setLoading(false);
        return true;
      } else {
        // Token is invalid, clear it
        Cookies.remove(authConfig.cookies.tokenName);
        Cookies.remove(authConfig.cookies.refreshTokenName);
        setUser(null);
        setLoading(false);
        return false;
      }
    } catch (err) {
      console.error('Auth check error:', err);
      setError('Failed to verify authentication');
      setUser(null);
      setLoading(false);
      return false;
    }
  }, []);

  // Login function
  const login = useCallback(async (email, otp) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, otp }),
        credentials: 'include', // Include cookies
      });

      const data = await response.json();

      if (response.ok) {
        setUser(data.user);
        setLoading(false);
        return { success: true, user: data.user };
      } else {
        setError(data.error || 'Login failed');
        setLoading(false);
        return { success: false, error: data.error || 'Login failed' };
      }
    } catch (err) {
      console.error('Login error:', err);
      const errorMessage = 'Network error during login';
      setError(errorMessage);
      setLoading(false);
      return { success: false, error: errorMessage };
    }
  }, []);

  // Logout function
  const logout = useCallback(async () => {
    try {
      setLoading(true);
      
      // Call logout API
      await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      // Clear client-side state regardless of API response
      Cookies.remove(authConfig.cookies.tokenName);
      Cookies.remove(authConfig.cookies.refreshTokenName);
      setUser(null);
      setError(null);
      setLoading(false);
      
      return true;
    } catch (err) {
      console.error('Logout error:', err);
      
      // Still clear client-side state
      Cookies.remove(authConfig.cookies.tokenName);
      Cookies.remove(authConfig.cookies.refreshTokenName);
      setUser(null);
      setError(null);
      setLoading(false);
      
      return true;
    }
  }, []);

  // Send OTP function
  const sendOtp = useCallback(async (email) => {
    try {
      setError(null);

      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        return { success: true, message: data.message, debug: data.debug };
      } else {
        setError(data.error || 'Failed to send OTP');
        return { 
          success: false, 
          error: data.error || 'Failed to send OTP',
          retryAfter: data.retryAfter 
        };
      }
    } catch (err) {
      console.error('Send OTP error:', err);
      const errorMessage = 'Network error while sending OTP';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, []);

  // Check if user has specific role
  const hasRole = useCallback((requiredRole) => {
    if (!user || !user.roles) return false;
    
    if (Array.isArray(requiredRole)) {
      return requiredRole.some(role => user.roles.includes(role));
    }
    
    return user.roles.includes(requiredRole);
  }, [user]);

  // Check if user has any admin role
  const isAdmin = useCallback(() => {
    return hasRole(['super_admin', 'admin']);
  }, [hasRole]);

  // Initialize auth check on mount
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Auto-refresh token periodically (optional)
  useEffect(() => {
    if (user) {
      const interval = setInterval(() => {
        checkAuth();
      }, 15 * 60 * 1000); // Check every 15 minutes

      return () => clearInterval(interval);
    }
  }, [user, checkAuth]);

  return {
    // State
    user,
    loading,
    error,
    isAuthenticated: !!user,
    
    // Methods
    login,
    logout,
    sendOtp,
    checkAuth,
    hasRole,
    isAdmin,
    
    // Utilities
    clearError: () => setError(null),
  };
}

/**
 * Authentication Context Provider Hook
 * 
 * For use with React Context if needed in the future.
 */
export function useAuthProvider() {
  const auth = useAuth();
  
  return {
    ...auth,
    // Additional provider-specific methods can be added here
  };
}
